'use client'
import CommonCalender from "@/components/common/CommonCalender"
import SearchInput from "@/components/common/SearchInput"
import TimePicker from "@/components/common/TimePicker"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import { ChangeEvent } from "react"
import { useDispatch, useSelector } from "react-redux"

const GeneralAvailability = () => {
    const { toggleAvailability, allTimeZoneList, generalAvailabilityList, toBookTime,
        toggleToBookTime, availabilityNote, toggleAvailabilityNote } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleToggleSection = (name: string, checked: boolean) => {
        dispatch(handleCoachInputChange({ name, value: checked }))
    }

    const handleSelectChange = (name: string, selected: EachSearchItem | null) => {
        dispatch(handleCoachInputChange({ name, value: selected }))
    }

    const handleAvailabilityCheckbox = (dayId: string, isAvailable: boolean | string) => {
        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId ? { ...item, isAvailable } : item
        );

        dispatch(
            handleCoachInputChange({
                name: 'generalAvailabilityList',
                value: updatedList,
            })
        );
    };


    const handleAvailabilityTime = (
        dayId: string,
        name: string,
        updatedTime: { hours: number; minutes: number; isPM: boolean }
    ) => {
        const formattedTime = `${updatedTime.hours.toString().padStart(2, '0')}:${updatedTime.minutes
            .toString()
            .padStart(2, '0')} ${updatedTime.isPM ? 'PM' : 'AM'}`;

        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId
                ? { ...item, [name]: formattedTime }
                : item
        );

        dispatch(
            handleCoachInputChange({
                name: 'generalAvailabilityList',
                value: updatedList,
            })
        );
    };

    const handleDateSelect = (date: Date | undefined) => {
        dispatch(handleCoachInputChange({ name: 'toBookTime', value: date }))
    }

    const handleOnChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        const { name, value } = event.target
        dispatch(handleCoachInputChange({ name, value }))
    }

    return (
        <>
            <div className="w-full h-full flex flex-col items-center gap-3 bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">General Availability</h3>
                    <Switch
                        name='toggleAvailability'
                        checked={toggleAvailability}
                        onCheckedChange={(checked) => handleToggleSection('toggleAvailability', checked)}
                    />
                </div>

                {toggleAvailability ?
                    <div className="flex flex-col gap-8 w-full">
                        <div className="flex flex-col gap-2">
                            <Label>Time Zone</Label>
                            <SearchInput
                                list={allTimeZoneList}
                                name='availableTimeZone'
                                onChange={handleSelectChange}
                                placeholder="Select Time Zone..."
                            />
                        </div>

                        <Table className="border-slate-400">
                            <TableHeader>
                                <TableRow className="text-lg">
                                    <TableHead className="font-bold w-[80px]">
                                        Day
                                    </TableHead>
                                    <TableHead className="font-bold text-center">Availability</TableHead>
                                    <TableHead className="font-bold text-center">Start Time</TableHead>
                                    <TableHead className="font-bold text-center">End Time</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {generalAvailabilityList?.map(each => (
                                    <TableRow key={each?.id}>
                                        <TableCell className="flex items-center gap-1 font-semibold">
                                            {each?.day}
                                            <PencilLine size={15} className="cursor-pointer" />
                                        </TableCell>
                                        <TableCell className="text-center">
                                            <Checkbox
                                                className="border-slate-500 h-4 w-4"
                                                checked={each?.isAvailable}
                                                onCheckedChange={(checked) => handleAvailabilityCheckbox(each?.id, checked)}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TimePicker
                                                onChange={(name, time) => handleAvailabilityTime(each?.id, name, time as any)}
                                                name={'startTime'}
                                                incrementMints={15}
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <TimePicker
                                                onChange={(name, time) => handleAvailabilityTime(each?.id, name, time as any)}
                                                name={'endTime'}
                                                incrementMints={15}
                                            />
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        <div className="flex flex-col gap-3">
                            <div className="flex items-center gap-2">
                                <Label>To Book Time</Label>
                                <Switch
                                    name={'toggleToBookTime'}
                                    checked={toggleToBookTime}
                                    onCheckedChange={(checked) => handleToggleSection('toggleToBookTime', checked)}
                                />
                            </div>
                            {toggleToBookTime && <CommonCalender
                                mode={'single'}
                                dateValue={toBookTime}
                                setDateFn={handleDateSelect}
                            />}
                        </div>

                        <div className="flex flex-col gap-3">
                            <div className="flex items-center gap-2">
                                <Label>Note</Label>
                                <Switch
                                    name={'toggleAvailabilityNote'}
                                    checked={toggleAvailabilityNote}
                                    onCheckedChange={(checked) => handleToggleSection('toggleAvailabilityNote', checked)}
                                />
                            </div>
                            {toggleAvailabilityNote &&
                                <>
                                    <Textarea
                                        name='availabilityNote'
                                        className="bg-white"
                                        placeholder="Write Notes..."
                                        value={availabilityNote}
                                        onChange={handleOnChange}
                                        maxLength={400}
                                        onInput={(e) => {
                                            const input = e.currentTarget;
                                            if (input.value.length > 400) {
                                                input.value = input.value.slice(0, 400);
                                            }
                                        }}
                                    />
                                    <div className="flex justify-end">
                                        <span className="text-red-500 text-sm">400 char max</span>
                                    </div>
                                </>
                            }
                        </div>
                    </div> : null}
            </div>
        </>
    )
}
export default GeneralAvailability