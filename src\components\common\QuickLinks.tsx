"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    <PERSON>alogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch } from "@/store"
import { deleteAthleteQuickLink, fetchAthleteQuickLinks, postAthleteQuickLinks, putAthleteQuickLink } from "@/store/slices/athlete/athleteProfileSlice"
import { EachQuickLinkItem } from "@/utils/interfaces"
import { Loader, PencilLine, Plus, Trash2 } from "lucide-react"
import { ChangeEvent, useEffect, useState } from "react"
import { useDispatch } from "react-redux"
import { Skeleton } from "../ui/skeleton"
import AlertPopup from "./AlertPopup"
import UpgradePremiumSection from "./UpgradePremiumSection"


interface IProps {
    origin: string;
    loading?: boolean;
    list: EachQuickLinkItem[]
    toggleQuickLinkSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
    fetchLoading?: boolean;
}

const emptyLink = { title: '', link: '' }

const QuickLinks = ({
    origin,
    fetchLoading,
    loading,
    list,
    toggleQuickLinkSection,
    onChangeToggleSection
}: IProps) => {
    const [quickLinksList, setQuicklinksList] = useState(list)
    const [addModal, setAddModal] = useState<boolean>(false);
    const [addLink, setAddLink] = useState(emptyLink)
    const [linkId, setLinkId] = useState(null)
    const dispatch = useDispatch<AppDispatch>()
    const { userId, isPremiumUser } = useTokenValues()
    const isAthletePremiumUser = origin === 'athlete' && isPremiumUser

    useEffect(() => {
        list && setQuicklinksList(list)
    }, [list])

    useEffect(() => {
        dispatch(fetchAthleteQuickLinks())
    }, [dispatch])

    const handleOnChangeAdd = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        setAddLink((prev) => ({ ...prev, [name]: value }))
    }

    const handleEditLink = (id) => {
        const filteredItem = quickLinksList?.find(each => each.id === id)
        setAddLink({ title: filteredItem?.title ?? '', link: filteredItem?.link ?? '' })
        setAddModal(true)
        setLinkId(id)
    }

    const apiSuccessStatus = async (api, action, fetchAPI) => {
        try {
            if (api.fulfilled.match(action)) {
                setAddModal(false)
                setAddLink(emptyLink)
                await dispatch((fetchAPI()))
            }
        } catch (error) {
            console.log(error)
        }
    };

    const handleOpenAddQuickLink = () => {
        setAddModal(!addModal)
        setLinkId(null)
        setAddLink(emptyLink)
    }

    const handleDeleteQuickLink = async (id) => {
        switch (origin) {
            case 'athlete':
                const resultAction = await dispatch(deleteAthleteQuickLink(id))
                if (deleteAthleteQuickLink.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteQuickLinks())
                }
                break;
            case 'coach':

                break;
            case 'business':

                break;
            default:
                break;
        }
    }


    const handleAddQuickLink = async () => {

        const payload = {
            userId,
            videos: [{
                videoTitle: addLink?.title,
                videoLink: addLink?.link
            }]
        }

        const updatePayload = {
            userId,
            videoTitle: addLink?.title,
            videoLink: addLink?.link
        }

        switch (origin) {
            case 'athlete':
                if (linkId) {
                    const resultAction = await dispatch(putAthleteQuickLink({ updatePayload, linkId }))
                    apiSuccessStatus(putAthleteQuickLink, resultAction, fetchAthleteQuickLinks)
                } else {
                    const resultAction = await dispatch(postAthleteQuickLinks(payload))
                    apiSuccessStatus(postAthleteQuickLinks, resultAction, fetchAthleteQuickLinks)
                }
                break;
            case 'coach':

                break;
            case 'business':

                break;
            default:
                break;
        }
    }

    if (fetchLoading) {
        return (
            <div className="flex flex-col items-center gap-6">
                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
            </div>
        )
    }

    return (
        <div className="w-full flex flex-col gap-8 bg-slate-100 p-4 rounded-lg">
            {!isAthletePremiumUser && <UpgradePremiumSection />}
            <div className="flex items-center justify-center gap-4">

                <Dialog open={addModal} onOpenChange={handleOpenAddQuickLink}>
                    <TooltipProvider>
                        <Tooltip>
                            <DialogTrigger asChild>
                                <TooltipTrigger asChild>
                                    <Button variant="outline" size="icon" disabled={!isAthletePremiumUser}>
                                        <Plus />
                                    </Button>
                                </TooltipTrigger>
                            </DialogTrigger>
                            <TooltipContent>
                                <p>Add Quick Video Link</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <DialogContent onInteractOutside={(event) => event.preventDefault()} className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Add Quick Link</DialogTitle>
                        </DialogHeader>
                        <div className="flex flex-col gap-6 mt-5">
                            <div className="flex flex-col gap-1">
                                <Label>Title</Label>
                                <Input
                                    placeholder="Enter video title"
                                    value={addLink?.title}
                                    name='title'
                                    onChange={handleOnChangeAdd}
                                />
                            </div>
                            <div className="flex flex-col gap-1">
                                <Label>Link</Label>
                                <Input
                                    placeholder="Enter video link"
                                    value={addLink?.link}
                                    name='link'
                                    onChange={handleOnChangeAdd}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit"
                                disabled={!addLink?.title || !addLink?.link}
                                onClick={handleAddQuickLink}
                            >
                                {loading ?
                                    <Loader className="animate-spin text-white w-10 h-10" />
                                    : 'Save'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                <h3 className="font-bold text-xl text-center">Quick Links</h3>
                <Switch
                    name='toggleQuickLinkSection'
                    checked={toggleQuickLinkSection}
                    onCheckedChange={onChangeToggleSection}
                    disabled={!isAthletePremiumUser}
                />
            </div>
            {toggleQuickLinkSection ?
                <div className="flex flex-col gap-4 ">
                    {quickLinksList?.length > 0 ? quickLinksList?.map((item, index) => (
                        <div key={item?.id} className="w-full flex items-center gap-5">
                            <div className='w-full flex items-center gap-1 justify-between bg-slate-200 hover:shadow-lg border rounded-xl'>
                                <a
                                    href={item?.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="pl-3 w-36 sm:w-56 md:w-64 lg:w-72 text-blue-600 hover:underline overflow-hidden text-ellipsis whitespace-nowrap block"
                                    title={item?.link}
                                >
                                    {item?.title}
                                </a>

                                <Button variant={'outline'} size={'icon'}
                                    onClick={() => handleEditLink(item?.id)}>
                                    <PencilLine />
                                </Button>
                            </div>
                            <AlertPopup
                                trigger={<Button variant={'destructive'} size={'icon'}
                                >
                                    <Trash2 />
                                </Button>
                                }
                                alertTitle="Confirm Deletion"
                                alertContent="Are you sure, you want to delete?"
                                action={() => handleDeleteQuickLink(item?.id)}
                            />
                        </div>
                    )) :
                        <div className="flex items-center justify-center">
                            <p className="text-gray-500 text-center">
                                Looks a little empty here... add your first video link!
                            </p>
                        </div>
                    }
                </div> : null}
        </div>
    )
}
export default QuickLinks