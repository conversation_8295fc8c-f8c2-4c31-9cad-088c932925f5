'use client'
import MultiSelectWithChip from "@/components/common/MultiSelectWithChip"
import SearchInput from "@/components/common/SearchInput"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice"
import { fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { EachSearchItem, Option } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

const CoachSportDetails = () => {
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice)
    const { selectedSport, addedSportLevelsList, addedSpecilitiesList } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch<AppDispatch>()

    useEffect(() => {
        dispatch(fetchAllSportLevels())
        dispatch(fetchAllSports())
    }, [dispatch])

    useEffect(() => {
        selectedSport && dispatch(fetchAllSpecialities(selectedSport?.value))
    }, [dispatch, selectedSport])

    const handleOnChange = (name: string, value: Option[] | EachSearchItem | null) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    return (
        <div className="bg-slate-100 rounded-lg p-5">
            <div className="flex justify-end">
                <Button variant={'outline'} size={'icon'}>
                    <PencilLine />
                </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col gap-1 col-span-2">
                    <Label>Sport Name</Label>
                    <SearchInput
                        list={allSportsList}
                        name="selectedSport"
                        placeholder="Select Sport Name"
                        value={selectedSport}
                        onChange={handleOnChange}
                    />
                </div>
                <div className="flex flex-col gap-1 col-span-1">
                    <Label>Primary Sport</Label>
                    <Switch />
                </div>
                <div className="flex flex-col gap-1 col-span-full">
                    <Label>Skill Level(s) I Train</Label>
                    <MultiSelectWithChip
                        options={allSportLevelList}
                        value={addedSportLevelsList}
                        name='addedSportLevelsList'
                        onChange={(selected) => handleOnChange('addedSportLevelsList', selected)}
                        placeholder="Select Level(s)..."
                    />
                </div>
                <div className="flex flex-col gap-1 col-span-full">
                    <Label>Position/Speciality</Label>
                    <MultiSelectWithChip
                        options={allSpecilitiesList}
                        value={addedSpecilitiesList}
                        name='addedSpecilitiesList'
                        onChange={(selected) => handleOnChange('addedSpecilitiesList', selected)}
                        placeholder="Select..."
                    />
                </div>
            </div>
            <div className="flex justify-end mt-2">
                <Button className="w-24">Save</Button>
            </div>
        </div>
    )
}
export default CoachSportDetails