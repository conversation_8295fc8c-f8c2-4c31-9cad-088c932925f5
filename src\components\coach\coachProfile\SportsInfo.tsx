'use client'
import SportItem from "@/components/common/SportItem"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { generateProfileUrl } from "@/utils/commonFunctions"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useDispatch, useSelector } from "react-redux"

const SportsInfo = () => {
    const { coachSelectedSportsList, toggleSportInfoSection } = useSelector((state: RootState) => state.coachProfile)
    const { profileData } = useLocalStoredInfo()
    const profileUrl = profileData && generateProfileUrl(profileData);
    const router = useRouter()
    const dispatch = useDispatch()

    const handleAddSport = () => {
        router.push(`${profileUrl}/addSport`)
    }

    const handleToggle = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleSportInfoSection', value: checked }))
    }

    return (
        <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
            <div className="flex flex-col items-center justify-center gap-4">
                <div className="flex flex-col items-center justify-center">
                    <div className="flex items-center gap-3">
                        <h3 className="font-bold text-xl text-center">Sports Info</h3>
                        <Switch checked={toggleSportInfoSection} onCheckedChange={handleToggle} />
                    </div>
                    <span className="text-center">(We recommend not more than 3 primary sports)</span>
                </div>
                <div className="self-end">
                    <Button onClick={handleAddSport} variant="outline" className="gap-2 border-slate-300 font-semibold hover:text-secondary">
                        <Plus />
                        Add Sport
                    </Button>
                </div>
            </div>

            {toggleSportInfoSection ? <div className="flex flex-col gap-5">
                {coachSelectedSportsList?.map(each => <SportItem roleId={3} key={each.id} item={each} />)}
            </div> : null}
        </div>
    )
}
export default SportsInfo